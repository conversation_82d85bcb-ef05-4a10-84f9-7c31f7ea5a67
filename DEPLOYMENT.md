# Deployment Guide - StyleHub E-commerce Store

This guide will help you deploy your StyleHub e-commerce application to production.

## 🚀 Quick Deployment (Vercel - Recommended)

### Prerequisites
- GitHub account
- Vercel account
- Supabase project set up
- Clerk application configured
- Razorpay account set up

### Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Configure environment variables (see below)
   - Deploy

3. **Environment Variables**
   Add these in your Vercel dashboard under Settings > Environment Variables:
   ```
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_...
   CLERK_SECRET_KEY=sk_live_...
   NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
   NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
   NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
   NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
   
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   
   NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_live_...
   RAZORPAY_KEY_SECRET=your_live_secret_here
   
   NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
   ```

## 🔧 Production Configuration

### 1. Supabase Production Setup

1. **Database Setup**
   - Run the schema from `database/schema.sql` in your Supabase SQL editor
   - Optionally run `database/seed.sql` for sample data
   - Enable Row Level Security (RLS) policies

2. **Storage Setup** (Optional)
   - Create a storage bucket for product images
   - Configure public access policies
   - Update image URLs in your products

### 2. Clerk Production Setup

1. **Domain Configuration**
   - Add your production domain to Clerk's allowed origins
   - Update redirect URLs to use your production domain
   - Configure social login providers if needed

2. **Webhooks** (Optional)
   - Set up webhooks for user events
   - Configure endpoint: `https://your-domain.com/api/webhooks/clerk`

### 3. Razorpay Production Setup

1. **Account Activation**
   - Complete KYC verification
   - Activate your live account
   - Get live API keys

2. **Webhook Configuration**
   - Set up payment webhooks
   - Configure endpoint: `https://your-domain.com/api/webhooks/razorpay`

## 🌐 Alternative Deployment Options

### Netlify

1. Connect your GitHub repository
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables
5. Deploy

### Railway

1. Connect your GitHub repository
2. Add environment variables
3. Deploy automatically

### DigitalOcean App Platform

1. Create new app from GitHub
2. Configure build settings
3. Add environment variables
4. Deploy

## 📊 Production Checklist

### Security
- [ ] All environment variables are set correctly
- [ ] Database RLS policies are enabled
- [ ] API keys are using live/production versions
- [ ] HTTPS is enabled
- [ ] CORS is properly configured

### Performance
- [ ] Images are optimized
- [ ] Database queries are optimized
- [ ] Caching is configured
- [ ] CDN is set up (if needed)

### Monitoring
- [ ] Error tracking is set up (Sentry, LogRocket, etc.)
- [ ] Analytics are configured (Google Analytics, etc.)
- [ ] Uptime monitoring is enabled
- [ ] Performance monitoring is active

### Testing
- [ ] All pages load correctly
- [ ] Authentication flow works
- [ ] Payment processing works
- [ ] Order creation and tracking work
- [ ] Admin panel is accessible
- [ ] Mobile responsiveness is verified

## 🔍 Post-Deployment Testing

### User Flow Testing
1. **Registration/Login**
   - Test user registration
   - Test user login
   - Test password reset

2. **Shopping Flow**
   - Browse products
   - Add items to cart
   - Proceed to checkout
   - Complete payment
   - Verify order creation

3. **Admin Flow**
   - Access admin panel
   - View dashboard metrics
   - Manage orders
   - Update product information

### Payment Testing
1. Use Razorpay test cards for initial testing
2. Process a small real transaction
3. Verify webhook delivery
4. Test refund process

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors
   - Verify all dependencies are installed
   - Check environment variable syntax

2. **Authentication Issues**
   - Verify Clerk domain configuration
   - Check redirect URLs
   - Ensure API keys are correct

3. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check RLS policies
   - Ensure database schema is up to date

4. **Payment Issues**
   - Verify Razorpay keys are live keys
   - Check webhook configuration
   - Ensure proper error handling

### Support Resources
- [Next.js Deployment Docs](https://nextjs.org/docs/deployment)
- [Vercel Support](https://vercel.com/support)
- [Supabase Docs](https://supabase.com/docs)
- [Clerk Docs](https://clerk.com/docs)
- [Razorpay Docs](https://razorpay.com/docs)

## 📈 Scaling Considerations

### Database
- Monitor query performance
- Set up read replicas if needed
- Implement database connection pooling

### CDN
- Use Vercel's built-in CDN
- Or configure Cloudflare for additional caching

### Monitoring
- Set up application monitoring
- Monitor database performance
- Track payment success rates
- Monitor user experience metrics

---

**Need Help?** Create an issue in the repository or contact <NAME_EMAIL>
