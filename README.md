# StyleHub - E-commerce Clothing Store

A complete e-commerce clothing store built with Next.js, featuring user authentication, shopping cart, payment processing, and admin panel.

## 🚀 Features

- **Product Catalog**: Browse clothing by categories (shirts, pants, dresses, etc.)
- **Shopping Cart**: Add/remove items with persistent cart state
- **User Authentication**: Secure login/signup with Clerk
- **Payment Processing**: Razorpay integration for Indian customers
- **Order Management**: Order history and tracking
- **Admin Panel**: Inventory and order management
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Search & Filtering**: Advanced product search and filtering

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **Payment Gateway**: Razorpay
- **Styling**: Tailwind CSS v4
- **Icons**: Lucide React
- **State Management**: React Context + hooks

## 📋 Prerequisites

Before you begin, ensure you have:
- Node.js 18+ installed
- A Supabase account and project
- A Clerk account and application
- A Razorpay account (for payments)

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd my-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Variables**

   Copy `.env.example` to `.env.local` and fill in your credentials:
   ```bash
   cp .env.example .env.local
   ```

   Update the following variables:
   ```env
   # Clerk Authentication
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   CLERK_SECRET_KEY=your_clerk_secret_key

   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Razorpay
   NEXT_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   ```

4. **Database Setup**

   Run the SQL scripts in your Supabase SQL editor:
   ```bash
   # First, run the schema creation
   # Copy and paste the contents of database/schema.sql

   # Then, run the seed data (optional)
   # Copy and paste the contents of database/seed.sql
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🗄️ Database Schema

The application uses the following main tables:
- `users` - User profiles (synced with Clerk)
- `categories` - Product categories
- `products` - Product catalog
- `orders` - Customer orders
- `order_items` - Items within orders

## 🔐 Authentication Setup

1. Create a Clerk application at [clerk.com](https://clerk.com)
2. Configure sign-in/sign-up pages
3. Add your domain to allowed origins
4. Copy the API keys to your `.env.local`

## 💳 Payment Setup

1. Create a Razorpay account at [razorpay.com](https://razorpay.com)
2. Get your API keys from the dashboard
3. Configure webhooks (optional)
4. Add keys to your `.env.local`

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # Reusable React components
├── contexts/           # React context providers
├── lib/                # Utility functions and configurations
└── database/           # SQL schema and seed files
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🔧 Admin Access

To access the admin panel:
1. Update the `adminEmails` array in `src/app/admin/page.tsx`
2. Add your email address
3. Visit `/admin` after signing in

## 📱 Features Overview

### Customer Features
- Browse products by category
- Search and filter products
- Add items to cart
- Secure checkout with Razorpay
- Order tracking and history
- User profile management

### Admin Features
- Dashboard with key metrics
- Product management
- Order management
- User management
- Basic analytics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
