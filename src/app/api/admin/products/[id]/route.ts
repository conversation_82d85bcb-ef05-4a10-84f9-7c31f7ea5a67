import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase'

// Helper function to check if user is admin
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  const adminEmails = ['<EMAIL>', '<EMAIL>']
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET - Fetch single product
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching product:', error)
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    return NextResponse.json({ product })
  } catch (error) {
    console.error('Product fetch error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update product
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      name,
      description,
      price,
      compare_at_price,
      category_id,
      images,
      sizes,
      colors,
      stock_quantity,
      is_active
    } = body

    // Validation
    if (!name || !description || !price || !category_id) {
      return NextResponse.json(
        { error: 'Missing required fields: name, description, price, category_id' },
        { status: 400 }
      )
    }

    if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      return NextResponse.json(
        { error: 'Price must be a valid positive number' },
        { status: 400 }
      )
    }

    // Get current product to check if name changed
    const { data: currentProduct } = await supabase
      .from('products')
      .select('name, slug')
      .eq('id', id)
      .single()

    let slug = currentProduct?.slug || generateSlug(name)

    // If name changed, generate new slug
    if (currentProduct && currentProduct.name !== name) {
      slug = generateSlug(name)
      let slugExists = true
      let counter = 1

      while (slugExists) {
        const { data } = await supabase
          .from('products')
          .select('id')
          .eq('slug', slug)
          .neq('id', id)
          .single()

        if (!data) {
          slugExists = false
        } else {
          slug = `${generateSlug(name)}-${counter}`
          counter++
        }
      }
    }

    // Update product
    const { data: product, error } = await supabase
      .from('products')
      .update({
        name,
        slug,
        description,
        price: parseFloat(price),
        compare_at_price: compare_at_price ? parseFloat(compare_at_price) : null,
        category_id,
        images: images || [],
        sizes: sizes || [],
        colors: colors || [],
        stock_quantity: parseInt(stock_quantity) || 0,
        is_active: is_active !== undefined ? is_active : true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      console.error('Error updating product:', error)
      return NextResponse.json({ error: 'Failed to update product' }, { status: 500 })
    }

    return NextResponse.json({ product })
  } catch (error) {
    console.error('Product update error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete product
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if product exists
    const { data: product } = await supabase
      .from('products')
      .select('id, name')
      .eq('id', id)
      .single()

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    // Check if product is in any orders (optional - you might want to prevent deletion)
    const { data: orderItems } = await supabase
      .from('order_items')
      .select('id')
      .eq('product_id', id)
      .limit(1)

    if (orderItems && orderItems.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete product that has been ordered. Consider deactivating it instead.' },
        { status: 400 }
      )
    }

    // Delete product
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting product:', error)
      return NextResponse.json({ error: 'Failed to delete product' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Product deleted successfully' })
  } catch (error) {
    console.error('Product deletion error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
