import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase'

// Helper function to check if user is admin
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  const adminEmails = ['<EMAIL>', '<EMAIL>']
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

// Helper function to generate slug from name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

// GET - Fetch all products for admin
export async function GET(request: NextRequest) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const status = searchParams.get('status') || ''

    const offset = (page - 1) * limit

    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*)
      `, { count: 'exact' })

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    if (category) {
      query = query.eq('category_id', category)
    }

    if (status === 'active') {
      query = query.eq('is_active', true)
    } else if (status === 'inactive') {
      query = query.eq('is_active', false)
    }

    // Apply pagination
    query = query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    const { data: products, error, count } = await query

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 })
    }

    return NextResponse.json({
      products: products || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    })
  } catch (error) {
    console.error('Products fetch error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create new product
export async function POST(request: NextRequest) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      price,
      compare_at_price,
      category_id,
      images,
      sizes,
      colors,
      stock_quantity,
      is_active = true
    } = body

    // Validation
    if (!name || !description || !price || !category_id) {
      return NextResponse.json(
        { error: 'Missing required fields: name, description, price, category_id' },
        { status: 400 }
      )
    }

    if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      return NextResponse.json(
        { error: 'Price must be a valid positive number' },
        { status: 400 }
      )
    }

    // Generate unique slug
    let slug = generateSlug(name)
    let slugExists = true
    let counter = 1

    while (slugExists) {
      const { data } = await supabase
        .from('products')
        .select('id')
        .eq('slug', slug)
        .single()

      if (!data) {
        slugExists = false
      } else {
        slug = `${generateSlug(name)}-${counter}`
        counter++
      }
    }

    // Create product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        name,
        slug,
        description,
        price: parseFloat(price),
        compare_at_price: compare_at_price ? parseFloat(compare_at_price) : null,
        category_id,
        images: images || [],
        sizes: sizes || [],
        colors: colors || [],
        stock_quantity: parseInt(stock_quantity) || 0,
        is_active
      })
      .select(`
        *,
        category:categories(*)
      `)
      .single()

    if (error) {
      console.error('Error creating product:', error)
      return NextResponse.json({ error: 'Failed to create product' }, { status: 500 })
    }

    return NextResponse.json({ product }, { status: 201 })
  } catch (error) {
    console.error('Product creation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
