import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase'

// Helper function to check if user is admin
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  const adminEmails = ['<EMAIL>', '<EMAIL>']
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

// POST - Bulk operations on products
export async function POST(request: NextRequest) {
  try {
    if (!(await isAdmin())) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, productIds } = body

    if (!action || !productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: action, productIds' },
        { status: 400 }
      )
    }

    let result
    let message

    switch (action) {
      case 'activate':
        result = await supabase
          .from('products')
          .update({ 
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .in('id', productIds)
        
        message = `${productIds.length} products activated successfully`
        break

      case 'deactivate':
        result = await supabase
          .from('products')
          .update({ 
            is_active: false,
            updated_at: new Date().toISOString()
          })
          .in('id', productIds)
        
        message = `${productIds.length} products deactivated successfully`
        break

      case 'delete':
        // Check if any products are in orders
        const { data: orderItems } = await supabase
          .from('order_items')
          .select('product_id')
          .in('product_id', productIds)

        const productsInOrders = new Set(orderItems?.map(item => item.product_id) || [])
        const productsToDelete = productIds.filter(id => !productsInOrders.has(id))
        const productsToKeep = productIds.filter(id => productsInOrders.has(id))

        if (productsToDelete.length > 0) {
          result = await supabase
            .from('products')
            .delete()
            .in('id', productsToDelete)
        }

        if (productsToKeep.length > 0) {
          message = `${productsToDelete.length} products deleted. ${productsToKeep.length} products could not be deleted because they have orders.`
        } else {
          message = `${productsToDelete.length} products deleted successfully`
        }
        break

      case 'update_stock':
        const { stockQuantity } = body
        if (stockQuantity === undefined || isNaN(parseInt(stockQuantity))) {
          return NextResponse.json(
            { error: 'Stock quantity must be a valid number' },
            { status: 400 }
          )
        }

        result = await supabase
          .from('products')
          .update({ 
            stock_quantity: parseInt(stockQuantity),
            updated_at: new Date().toISOString()
          })
          .in('id', productIds)
        
        message = `Stock updated to ${stockQuantity} for ${productIds.length} products`
        break

      case 'update_category':
        const { categoryId } = body
        if (!categoryId) {
          return NextResponse.json(
            { error: 'Category ID is required' },
            { status: 400 }
          )
        }

        // Verify category exists
        const { data: category } = await supabase
          .from('categories')
          .select('id')
          .eq('id', categoryId)
          .single()

        if (!category) {
          return NextResponse.json(
            { error: 'Category not found' },
            { status: 404 }
          )
        }

        result = await supabase
          .from('products')
          .update({ 
            category_id: categoryId,
            updated_at: new Date().toISOString()
          })
          .in('id', productIds)
        
        message = `Category updated for ${productIds.length} products`
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: activate, deactivate, delete, update_stock, update_category' },
          { status: 400 }
        )
    }

    if (result?.error) {
      console.error('Bulk operation error:', result.error)
      return NextResponse.json({ error: 'Failed to perform bulk operation' }, { status: 500 })
    }

    return NextResponse.json({ 
      message,
      affectedCount: productIds.length
    })
  } catch (error) {
    console.error('Bulk operation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
