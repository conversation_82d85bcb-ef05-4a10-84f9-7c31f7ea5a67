import { redirect } from 'next/navigation'
import { currentUser } from '@clerk/nextjs/server'
import { getCategories } from '@/lib/products'
import CreateProductForm from '@/components/admin/CreateProductForm'

// Simple admin check
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  const adminEmails = ['<EMAIL>', '<EMAIL>']
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

export default async function NewProductPage() {
  if (!(await isAdmin())) {
    redirect('/')
  }

  const categories = await getCategories()

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Product</h1>
          <p className="text-gray-600">Add a new product to your catalog</p>
        </div>

        <CreateProductForm categories={categories} />
      </div>
    </div>
  )
}
