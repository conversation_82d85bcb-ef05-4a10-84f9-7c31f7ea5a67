import { redirect, notFound } from 'next/navigation'
import { currentUser } from '@clerk/nextjs/server'
import { getCategories } from '@/lib/products'
import { supabase } from '@/lib/supabase'
import EditProductForm from '@/components/admin/EditProductForm'

// Simple admin check
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  const adminEmails = ['<EMAIL>', '<EMAIL>']
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

interface EditProductPageProps {
  params: Promise<{ id: string }>
}

export default async function EditProductPage({ params }: EditProductPageProps) {
  if (!(await isAdmin())) {
    redirect('/')
  }

  const { id } = await params
  const categories = await getCategories()

  // Fetch product data
  const { data: product, error } = await supabase
    .from('products')
    .select(`
      *,
      category:categories(*)
    `)
    .eq('id', id)
    .single()

  if (error || !product) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Edit Product</h1>
          <p className="text-gray-600">Update product information</p>
        </div>

        <EditProductForm product={product} categories={categories} />
      </div>
    </div>
  )
}
