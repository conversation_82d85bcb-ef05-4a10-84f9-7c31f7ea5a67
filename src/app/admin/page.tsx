import { redirect } from 'next/navigation'
import { currentUser } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase'
import { formatPrice } from '@/lib/products'
import { Package, ShoppingCart, Users, TrendingUp } from 'lucide-react'
import Link from 'next/link'

// Simple admin check - in production, you'd want proper role-based access control
async function isAdmin() {
  const user = await currentUser()
  if (!user) return false
  
  // Check if user email is in admin list (you can store this in database)
  const adminEmails = ['<EMAIL>', '<EMAIL>'] // Add your admin emails
  return adminEmails.includes(user.emailAddresses[0]?.emailAddress || '')
}

export default async function AdminDashboard() {
  if (!(await isAdmin())) {
    redirect('/')
  }

  // Fetch dashboard stats
  const [
    { count: totalProducts },
    { count: totalOrders },
    { count: totalUsers },
    { data: recentOrders }
  ] = await Promise.all([
    supabase.from('products').select('*', { count: 'exact', head: true }),
    supabase.from('orders').select('*', { count: 'exact', head: true }),
    supabase.from('users').select('*', { count: 'exact', head: true }),
    supabase
      .from('orders')
      .select(`
        *,
        users(first_name, last_name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(5)
  ])

  // Calculate total revenue
  const { data: paidOrders } = await supabase
    .from('orders')
    .select('total_amount')
    .eq('payment_status', 'paid')

  const totalRevenue = paidOrders?.reduce((sum, order) => sum + order.total_amount, 0) || 0

  const stats = [
    {
      name: 'Total Products',
      value: totalProducts || 0,
      icon: Package,
      color: 'bg-blue-500'
    },
    {
      name: 'Total Orders',
      value: totalOrders || 0,
      icon: ShoppingCart,
      color: 'bg-green-500'
    },
    {
      name: 'Total Users',
      value: totalUsers || 0,
      icon: Users,
      color: 'bg-purple-500'
    },
    {
      name: 'Total Revenue',
      value: formatPrice(totalRevenue),
      icon: TrendingUp,
      color: 'bg-yellow-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Manage your e-commerce store</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Orders */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Recent Orders</h2>
                <Link
                  href="/admin/orders"
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  View all
                </Link>
              </div>
            </div>
            <div className="p-6">
              {recentOrders && recentOrders.length > 0 ? (
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">#{order.order_number}</p>
                        <p className="text-sm text-gray-600">
                          {order.users?.first_name} {order.users?.last_name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(order.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          {formatPrice(order.total_amount)}
                        </p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                          order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No orders yet</p>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-4">
                <Link
                  href="/admin/products"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Package className="h-8 w-8 text-blue-600 mr-4" />
                  <div>
                    <h3 className="font-medium text-gray-900">Manage Products</h3>
                    <p className="text-sm text-gray-600">Add, edit, or remove products from your catalog</p>
                  </div>
                </Link>
                
                <Link
                  href="/admin/orders"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ShoppingCart className="h-8 w-8 text-green-600 mr-4" />
                  <div>
                    <h3 className="font-medium text-gray-900">Manage Orders</h3>
                    <p className="text-sm text-gray-600">View and update order status</p>
                  </div>
                </Link>
                
                <Link
                  href="/admin/users"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Users className="h-8 w-8 text-purple-600 mr-4" />
                  <div>
                    <h3 className="font-medium text-gray-900">Manage Users</h3>
                    <p className="text-sm text-gray-600">View customer information</p>
                  </div>
                </Link>
                
                <Link
                  href="/admin/analytics"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <TrendingUp className="h-8 w-8 text-yellow-600 mr-4" />
                  <div>
                    <h3 className="font-medium text-gray-900">Analytics</h3>
                    <p className="text-sm text-gray-600">View sales and performance data</p>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
