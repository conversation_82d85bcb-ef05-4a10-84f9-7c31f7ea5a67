import { getProducts, getCategories } from '@/lib/products'
import ProductCard from '@/components/ProductCard'
import SearchAndFilter from '@/components/SearchAndFilter'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'All Products - StyleHub',
  description: 'Browse our complete collection of premium clothing and fashion accessories.',
}

interface ProductsPageProps {
  searchParams: Promise<{
    page?: string
    search?: string
    category?: string
    sort?: string
  }>
}

export default async function ProductsPage({ searchParams }: ProductsPageProps) {
  const { page = '1', search, category, sort = 'newest' } = await searchParams
  
  const currentPage = parseInt(page)
  const productsPerPage = 12
  const offset = (currentPage - 1) * productsPerPage

  // Get categories for filter
  const categories = await getCategories()

  // Get products with filters
  const { products, total } = await getProducts({
    limit: productsPerPage,
    offset,
    search,
    categorySlug: category
  })

  // Sort products
  const sortedProducts = [...products]
  switch (sort) {
    case 'price-low':
      sortedProducts.sort((a, b) => a.price - b.price)
      break
    case 'price-high':
      sortedProducts.sort((a, b) => b.price - a.price)
      break
    case 'name':
      sortedProducts.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'newest':
    default:
      // Already sorted by created_at desc from the query
      break
  }

  const totalPages = Math.ceil(total / productsPerPage)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">All Products</h1>
          <p className="text-gray-600">
            {total} {total === 1 ? 'product' : 'products'} found
            {search && ` for "${search}"`}
            {category && ` in ${categories.find(c => c.slug === category)?.name}`}
          </p>
        </div>

        {/* Search and Filter */}
        <SearchAndFilter categories={categories} />

        {/* Products Grid */}
        {sortedProducts.length > 0 ? (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {sortedProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2">
                {currentPage > 1 && (
                  <a
                    href={`/products?page=${currentPage - 1}${search ? `&search=${search}` : ''}${category ? `&category=${category}` : ''}${sort !== 'newest' ? `&sort=${sort}` : ''}`}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Previous
                  </a>
                )}
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                  return (
                    <a
                      key={pageNum}
                      href={`/products?page=${pageNum}${search ? `&search=${search}` : ''}${category ? `&category=${category}` : ''}${sort !== 'newest' ? `&sort=${sort}` : ''}`}
                      className={`px-4 py-2 text-sm font-medium rounded-md ${
                        pageNum === currentPage
                          ? 'bg-gray-900 text-white'
                          : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </a>
                  )
                })}
                
                {currentPage < totalPages && (
                  <a
                    href={`/products?page=${currentPage + 1}${search ? `&search=${search}` : ''}${category ? `&category=${category}` : ''}${sort !== 'newest' ? `&sort=${sort}` : ''}`}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Next
                  </a>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No products found
              {search && ` for "${search}"`}
              {category && ` in ${categories.find(c => c.slug === category)?.name}`}.
            </p>
            <p className="text-gray-400 mt-2">Try adjusting your search or filters.</p>
          </div>
        )}
      </div>
    </div>
  )
}
