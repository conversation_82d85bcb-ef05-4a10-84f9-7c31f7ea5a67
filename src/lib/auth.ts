import { currentUser } from '@clerk/nextjs/server'
import { supabase } from './supabase'
import { User } from './types'

export async function syncUserWithDatabase() {
  const user = await currentUser()
  
  if (!user) {
    return null
  }

  // Check if user exists in our database
  const { data: existingUser, error: fetchError } = await supabase
    .from('users')
    .select('*')
    .eq('clerk_user_id', user.id)
    .single()

  if (fetchError && fetchError.code !== 'PGRST116') {
    console.error('Error fetching user:', fetchError)
    return null
  }

  // If user doesn't exist, create them
  if (!existingUser) {
    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert({
        clerk_user_id: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        first_name: user.firstName || '',
        last_name: user.lastName || '',
        phone: user.phoneNumbers[0]?.phoneNumber || null,
      })
      .select()
      .single()

    if (insertError) {
      console.error('Error creating user:', insertError)
      return null
    }

    return newUser as User
  }

  // Update existing user if data has changed
  const shouldUpdate = 
    existingUser.email !== (user.emailAddresses[0]?.emailAddress || '') ||
    existingUser.first_name !== (user.firstName || '') ||
    existingUser.last_name !== (user.lastName || '') ||
    existingUser.phone !== (user.phoneNumbers[0]?.phoneNumber || null)

  if (shouldUpdate) {
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({
        email: user.emailAddresses[0]?.emailAddress || '',
        first_name: user.firstName || '',
        last_name: user.lastName || '',
        phone: user.phoneNumbers[0]?.phoneNumber || null,
        updated_at: new Date().toISOString(),
      })
      .eq('clerk_user_id', user.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating user:', updateError)
      return existingUser as User
    }

    return updatedUser as User
  }

  return existingUser as User
}

export async function getCurrentUser(): Promise<User | null> {
  return await syncUserWithDatabase()
}
