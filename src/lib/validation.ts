import { ProductFormData } from '@/components/admin/ProductForm'

export interface ValidationError {
  field: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

export function validateProductForm(data: ProductFormData): ValidationResult {
  const errors: ValidationError[] = []

  // Name validation
  if (!data.name.trim()) {
    errors.push({ field: 'name', message: 'Product name is required' })
  } else if (data.name.trim().length < 3) {
    errors.push({ field: 'name', message: 'Product name must be at least 3 characters long' })
  } else if (data.name.trim().length > 100) {
    errors.push({ field: 'name', message: 'Product name must be less than 100 characters' })
  }

  // Description validation
  if (!data.description.trim()) {
    errors.push({ field: 'description', message: 'Product description is required' })
  } else if (data.description.trim().length < 10) {
    errors.push({ field: 'description', message: 'Description must be at least 10 characters long' })
  } else if (data.description.trim().length > 2000) {
    errors.push({ field: 'description', message: 'Description must be less than 2000 characters' })
  }

  // Price validation
  if (!data.price.trim()) {
    errors.push({ field: 'price', message: 'Price is required' })
  } else {
    const price = parseFloat(data.price)
    if (isNaN(price)) {
      errors.push({ field: 'price', message: 'Price must be a valid number' })
    } else if (price <= 0) {
      errors.push({ field: 'price', message: 'Price must be greater than 0' })
    } else if (price > 1000000) {
      errors.push({ field: 'price', message: 'Price must be less than ₹10,00,000' })
    }
  }

  // Compare at price validation
  if (data.compare_at_price.trim()) {
    const compareAtPrice = parseFloat(data.compare_at_price)
    const price = parseFloat(data.price)
    
    if (isNaN(compareAtPrice)) {
      errors.push({ field: 'compare_at_price', message: 'Compare at price must be a valid number' })
    } else if (compareAtPrice <= 0) {
      errors.push({ field: 'compare_at_price', message: 'Compare at price must be greater than 0' })
    } else if (!isNaN(price) && compareAtPrice <= price) {
      errors.push({ field: 'compare_at_price', message: 'Compare at price must be greater than the selling price' })
    }
  }

  // Category validation
  if (!data.category_id) {
    errors.push({ field: 'category_id', message: 'Category is required' })
  }

  // Stock quantity validation
  if (!data.stock_quantity.trim()) {
    errors.push({ field: 'stock_quantity', message: 'Stock quantity is required' })
  } else {
    const stockQuantity = parseInt(data.stock_quantity)
    if (isNaN(stockQuantity)) {
      errors.push({ field: 'stock_quantity', message: 'Stock quantity must be a valid number' })
    } else if (stockQuantity < 0) {
      errors.push({ field: 'stock_quantity', message: 'Stock quantity cannot be negative' })
    } else if (stockQuantity > 100000) {
      errors.push({ field: 'stock_quantity', message: 'Stock quantity must be less than 100,000' })
    }
  }

  // Images validation
  if (data.images.length === 0) {
    errors.push({ field: 'images', message: 'At least one product image is required' })
  } else if (data.images.length > 10) {
    errors.push({ field: 'images', message: 'Maximum 10 images allowed' })
  }

  // Validate image URLs
  data.images.forEach((image, index) => {
    if (!isValidImageUrl(image)) {
      errors.push({ 
        field: 'images', 
        message: `Image ${index + 1} has an invalid URL format` 
      })
    }
  })

  // Sizes validation
  if (data.sizes.length === 0) {
    errors.push({ field: 'sizes', message: 'At least one size must be selected' })
  }

  // Colors validation
  if (data.colors.length === 0) {
    errors.push({ field: 'colors', message: 'At least one color must be selected' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function isValidImageUrl(url: string): boolean {
  try {
    // Check if it's a data URL (for uploaded files)
    if (url.startsWith('data:image/')) {
      return true
    }

    // Check if it's a valid HTTP/HTTPS URL
    const urlObj = new URL(url)
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false
    }

    // Check if URL ends with common image extensions
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg']
    const pathname = urlObj.pathname.toLowerCase()
    
    return imageExtensions.some(ext => pathname.endsWith(ext)) || 
           pathname.includes('/image/') || 
           urlObj.hostname.includes('images') ||
           urlObj.hostname.includes('cdn')
  } catch {
    return false
  }
}

export function validateSlug(slug: string): ValidationResult {
  const errors: ValidationError[] = []

  if (!slug.trim()) {
    errors.push({ field: 'slug', message: 'Slug is required' })
  } else if (!/^[a-z0-9-]+$/.test(slug)) {
    errors.push({ field: 'slug', message: 'Slug can only contain lowercase letters, numbers, and hyphens' })
  } else if (slug.length < 3) {
    errors.push({ field: 'slug', message: 'Slug must be at least 3 characters long' })
  } else if (slug.length > 100) {
    errors.push({ field: 'slug', message: 'Slug must be less than 100 characters' })
  } else if (slug.startsWith('-') || slug.endsWith('-')) {
    errors.push({ field: 'slug', message: 'Slug cannot start or end with a hyphen' })
  } else if (slug.includes('--')) {
    errors.push({ field: 'slug', message: 'Slug cannot contain consecutive hyphens' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function sanitizeInput(input: string): string {
  return input.trim().replace(/\s+/g, ' ')
}

export function generateSlugFromName(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export function formatValidationErrors(errors: ValidationError[]): Record<string, string> {
  const formattedErrors: Record<string, string> = {}
  
  errors.forEach(error => {
    if (!formattedErrors[error.field]) {
      formattedErrors[error.field] = error.message
    }
  })
  
  return formattedErrors
}
