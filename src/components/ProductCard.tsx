import Link from 'next/link'
import Image from 'next/image'
import { Product } from '@/lib/types'
import { formatPrice, calculateDiscountPercentage } from '@/lib/products'
import { ShoppingBag } from 'lucide-react'

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const discountPercentage = calculateDiscountPercentage(product.price, product.compare_at_price)

  return (
    <div className="group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      <Link href={`/products/${product.slug}`}>
        <div className="aspect-square overflow-hidden rounded-t-lg bg-gray-200">
          {product.images && product.images.length > 0 ? (
            <Image
              src={product.images[0]}
              alt={product.name}
              width={400}
              height={400}
              className="h-full w-full object-cover object-center group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gray-100">
              <ShoppingBag className="h-16 w-16 text-gray-400" />
            </div>
          )}
          
          {/* Discount badge */}
          {discountPercentage > 0 && (
            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
              {discountPercentage}% OFF
            </div>
          )}
        </div>
      </Link>

      <div className="p-4">
        <Link href={`/products/${product.slug}`}>
          <h3 className="text-sm font-medium text-gray-900 hover:text-gray-700 transition-colors">
            {product.name}
          </h3>
        </Link>
        
        {product.category && (
          <p className="text-xs text-gray-500 mt-1">{product.category.name}</p>
        )}

        <div className="mt-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-semibold text-gray-900">
              {formatPrice(product.price)}
            </span>
            {product.compare_at_price && product.compare_at_price > product.price && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.compare_at_price)}
              </span>
            )}
          </div>
        </div>

        {/* Stock status */}
        <div className="mt-2">
          {product.stock_quantity > 0 ? (
            <span className="text-xs text-green-600">In Stock</span>
          ) : (
            <span className="text-xs text-red-600">Out of Stock</span>
          )}
        </div>

        {/* Available sizes preview */}
        {product.sizes && product.sizes.length > 0 && (
          <div className="mt-2">
            <p className="text-xs text-gray-500">
              Sizes: {product.sizes.slice(0, 3).join(', ')}
              {product.sizes.length > 3 && '...'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
