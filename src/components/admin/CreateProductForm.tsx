'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Category } from '@/lib/types'
import ProductForm, { ProductFormData } from './ProductForm'

interface CreateProductFormProps {
  categories: Category[]
}

export default function CreateProductForm({ categories }: CreateProductFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: ProductFormData) => {
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await response.json()
        alert('Product created successfully!')
        router.push('/admin/products')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create product')
      }
    } catch (error) {
      console.error('Create product error:', error)
      alert('Failed to create product')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ProductForm
      categories={categories}
      onSubmit={handleSubmit}
      isLoading={isLoading}
    />
  )
}
