'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Product, Category } from '@/lib/types'
import ProductForm, { ProductFormData } from './ProductForm'

interface EditProductFormProps {
  product: Product
  categories: Category[]
}

export default function EditProductForm({ product, categories }: EditProductFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: ProductFormData) => {
    setIsLoading(true)
    
    try {
      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await response.json()
        alert('Product updated successfully!')
        router.push('/admin/products')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update product')
      }
    } catch (error) {
      console.error('Update product error:', error)
      alert('Failed to update product')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ProductForm
      product={product}
      categories={categories}
      onSubmit={handleSubmit}
      isLoading={isLoading}
    />
  )
}
