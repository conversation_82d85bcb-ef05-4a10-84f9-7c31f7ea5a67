'use client'

import { useState } from 'react'
import { Product } from '@/lib/types'
import { useCart } from '@/contexts/CartContext'
import { ShoppingBag, Plus, Minus } from 'lucide-react'

interface AddToCartButtonProps {
  product: Product
}

export default function AddToCartButton({ product }: AddToCartButtonProps) {
  const { addItem } = useCart()
  const [selectedSize, setSelectedSize] = useState(product.sizes?.[0] || '')
  const [selectedColor, setSelectedColor] = useState(product.colors?.[0] || '')
  const [quantity, setQuantity] = useState(1)
  const [isAdding, setIsAdding] = useState(false)

  const handleAddToCart = async () => {
    if (!selectedSize || !selectedColor) {
      alert('Please select size and color')
      return
    }

    if (product.stock_quantity <= 0) {
      alert('Product is out of stock')
      return
    }

    setIsAdding(true)
    
    try {
      addItem(product, selectedSize, selectedColor, quantity)
      
      // Show success feedback
      const button = document.getElementById('add-to-cart-btn')
      if (button) {
        button.textContent = 'Added to Cart!'
        setTimeout(() => {
          button.textContent = 'Add to Cart'
        }, 2000)
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      alert('Failed to add item to cart')
    } finally {
      setIsAdding(false)
    }
  }

  const incrementQuantity = () => {
    if (quantity < product.stock_quantity) {
      setQuantity(quantity + 1)
    }
  }

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1)
    }
  }

  return (
    <div className="space-y-4">
      {/* Size Selection */}
      {product.sizes && product.sizes.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Size
          </label>
          <div className="flex flex-wrap gap-2">
            {product.sizes.map((size) => (
              <button
                key={size}
                onClick={() => setSelectedSize(size)}
                className={`px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                  selectedSize === size
                    ? 'border-gray-900 bg-gray-900 text-white'
                    : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                }`}
              >
                {size}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Color Selection */}
      {product.colors && product.colors.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Color
          </label>
          <div className="flex flex-wrap gap-2">
            {product.colors.map((color) => (
              <button
                key={color}
                onClick={() => setSelectedColor(color)}
                className={`px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                  selectedColor === color
                    ? 'border-gray-900 bg-gray-900 text-white'
                    : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                }`}
              >
                {color}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Quantity Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quantity
        </label>
        <div className="flex items-center space-x-3">
          <button
            onClick={decrementQuantity}
            disabled={quantity <= 1}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Minus className="h-4 w-4" />
          </button>
          <span className="text-lg font-medium w-12 text-center">{quantity}</span>
          <button
            onClick={incrementQuantity}
            disabled={quantity >= product.stock_quantity}
            className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Add to Cart Button */}
      <button
        id="add-to-cart-btn"
        onClick={handleAddToCart}
        disabled={isAdding || product.stock_quantity <= 0}
        className={`w-full flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white transition-colors ${
          product.stock_quantity <= 0
            ? 'bg-gray-400 cursor-not-allowed'
            : isAdding
            ? 'bg-gray-600'
            : 'bg-gray-900 hover:bg-gray-800'
        }`}
      >
        <ShoppingBag className="h-5 w-5 mr-2" />
        {product.stock_quantity <= 0 
          ? 'Out of Stock' 
          : isAdding 
          ? 'Adding...' 
          : 'Add to Cart'
        }
      </button>
    </div>
  )
}
