-- Insert sample categories
INSERT INTO categories (name, slug, description, image_url) VALUES
('Shirts', 'shirts', 'Casual and formal shirts for all occasions', '/images/categories/shirts.jpg'),
('Pants', 'pants', 'Comfortable pants and trousers', '/images/categories/pants.jpg'),
('Dresses', 'dresses', 'Elegant dresses for women', '/images/categories/dresses.jpg'),
('T-Shirts', 't-shirts', 'Casual t-shirts and tops', '/images/categories/tshirts.jpg'),
('Jeans', 'jeans', 'Premium denim jeans', '/images/categories/jeans.jpg'),
('Accessories', 'accessories', 'Fashion accessories and more', '/images/categories/accessories.jpg');

-- Insert sample products
INSERT INTO products (name, slug, description, price, compare_at_price, category_id, images, sizes, colors, stock_quantity) VALUES
-- Shirts
('Classic White Shirt', 'classic-white-shirt', 'A timeless white shirt perfect for formal and casual occasions. Made from premium cotton with a comfortable fit.', 2499.00, 3499.00, (SELECT id FROM categories WHERE slug = 'shirts'), 
 ARRAY['/images/products/white-shirt-1.jpg', '/images/products/white-shirt-2.jpg'], 
 ARRAY['S', 'M', 'L', 'XL', 'XXL'], 
 ARRAY['White', 'Light Blue'], 50),

('Casual Checkered Shirt', 'casual-checkered-shirt', 'Comfortable checkered shirt for everyday wear. Soft cotton blend with modern fit.', 1999.00, 2799.00, (SELECT id FROM categories WHERE slug = 'shirts'), 
 ARRAY['/images/products/checkered-shirt-1.jpg', '/images/products/checkered-shirt-2.jpg'], 
 ARRAY['S', 'M', 'L', 'XL'], 
 ARRAY['Blue', 'Red', 'Green'], 35),

-- T-Shirts
('Premium Cotton T-Shirt', 'premium-cotton-tshirt', 'Super soft premium cotton t-shirt with perfect fit. Available in multiple colors.', 899.00, 1299.00, (SELECT id FROM categories WHERE slug = 't-shirts'), 
 ARRAY['/images/products/cotton-tshirt-1.jpg', '/images/products/cotton-tshirt-2.jpg'], 
 ARRAY['XS', 'S', 'M', 'L', 'XL'], 
 ARRAY['Black', 'White', 'Navy', 'Gray'], 100),

('Graphic Print T-Shirt', 'graphic-print-tshirt', 'Trendy graphic print t-shirt with unique designs. Perfect for casual outings.', 1299.00, 1799.00, (SELECT id FROM categories WHERE slug = 't-shirts'), 
 ARRAY['/images/products/graphic-tshirt-1.jpg', '/images/products/graphic-tshirt-2.jpg'], 
 ARRAY['S', 'M', 'L', 'XL'], 
 ARRAY['Black', 'White'], 75),

-- Pants
('Formal Black Trousers', 'formal-black-trousers', 'Elegant black formal trousers perfect for office wear. Wrinkle-resistant fabric.', 2799.00, 3999.00, (SELECT id FROM categories WHERE slug = 'pants'), 
 ARRAY['/images/products/black-trousers-1.jpg', '/images/products/black-trousers-2.jpg'], 
 ARRAY['28', '30', '32', '34', '36', '38'], 
 ARRAY['Black', 'Navy'], 40),

('Casual Chinos', 'casual-chinos', 'Comfortable casual chinos for everyday wear. Versatile and stylish.', 2199.00, 2999.00, (SELECT id FROM categories WHERE slug = 'pants'), 
 ARRAY['/images/products/chinos-1.jpg', '/images/products/chinos-2.jpg'], 
 ARRAY['28', '30', '32', '34', '36'], 
 ARRAY['Khaki', 'Navy', 'Olive'], 60),

-- Jeans
('Classic Blue Jeans', 'classic-blue-jeans', 'Timeless blue jeans with perfect fit. Premium denim quality.', 3499.00, 4999.00, (SELECT id FROM categories WHERE slug = 'jeans'), 
 ARRAY['/images/products/blue-jeans-1.jpg', '/images/products/blue-jeans-2.jpg'], 
 ARRAY['28', '30', '32', '34', '36', '38'], 
 ARRAY['Light Blue', 'Dark Blue'], 45),

('Skinny Fit Jeans', 'skinny-fit-jeans', 'Modern skinny fit jeans with stretch comfort. Perfect for a contemporary look.', 3999.00, 5499.00, (SELECT id FROM categories WHERE slug = 'jeans'), 
 ARRAY['/images/products/skinny-jeans-1.jpg', '/images/products/skinny-jeans-2.jpg'], 
 ARRAY['28', '30', '32', '34', '36'], 
 ARRAY['Black', 'Dark Blue'], 30),

-- Dresses
('Elegant Evening Dress', 'elegant-evening-dress', 'Beautiful evening dress perfect for special occasions. Flowing fabric with elegant design.', 4999.00, 6999.00, (SELECT id FROM categories WHERE slug = 'dresses'), 
 ARRAY['/images/products/evening-dress-1.jpg', '/images/products/evening-dress-2.jpg'], 
 ARRAY['XS', 'S', 'M', 'L', 'XL'], 
 ARRAY['Black', 'Navy', 'Burgundy'], 25),

('Casual Summer Dress', 'casual-summer-dress', 'Light and comfortable summer dress. Perfect for casual outings and warm weather.', 2499.00, 3499.00, (SELECT id FROM categories WHERE slug = 'dresses'), 
 ARRAY['/images/products/summer-dress-1.jpg', '/images/products/summer-dress-2.jpg'], 
 ARRAY['XS', 'S', 'M', 'L'], 
 ARRAY['Floral', 'Solid Blue', 'White'], 40);

-- Note: In a real application, you would also insert actual image files and update the image URLs accordingly
